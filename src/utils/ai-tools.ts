import { createOpenAI } from '@ai-sdk/openai';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { createDeepSeek } from '@ai-sdk/deepseek';
import { createGroq } from '@ai-sdk/groq';
import { LanguageModelV1 } from 'ai';
import { ProxyConfig } from '@/lib/proxy';

export type ApiKey = {
  service: string;
  key: string;
  addedAt: string;
};

export type AIConfig = {
  model: string;
  apiKeys: Array<ApiKey>;
};

/**
 * Initializes an AI client based on the provided configuration
 * Falls back to default OpenAI configuration if no config is provided
 */
export function initializeAIClient(config?: AIConfig, isPro?: boolean, useThinking?: boolean) {
  console.log('🔍 DEBUG: initializeAIClient called');
  console.log('- config:', JSON.stringify(config, null, 2));
  console.log('- isPro:', isPro);
  console.log('- useThinking:', useThinking);

  // 创建代理 fetch 函数（按照教程方式）
  const createProxyFetch = () => {
    const proxyAgent = ProxyConfig.getProxyAgent();
    if (!proxyAgent) {
      return undefined;
    }

    // 返回使用 node-fetch + agent 的函数（使用 any 避免类型问题）
    return async (input: any, init?: any) => {
      const nodeFetch = await import('node-fetch');
      const url = typeof input === 'string' ? input : input.toString();
      return nodeFetch.default(url, { ...init, agent: proxyAgent }) as any;
    };
  };

  const proxyFetch = createProxyFetch();

  // Handle Pro subscription with environment variables
  if (isPro && config) {
    console.log('🔍 DEBUG: Pro user with config');
    const { model } = config;
    console.log('- Selected model:', model);

    if (model.startsWith('claude')) {
      if (!process.env.ANTHROPIC_API_KEY) throw new Error('Anthropic API key not found');
      return createAnthropic({
        apiKey: process.env.ANTHROPIC_API_KEY,
        ...(proxyFetch && { fetch: proxyFetch })
      })(model) as LanguageModelV1;
    }

    if (model.startsWith('gemini')) {
      if (!process.env.GEMINI_API_KEY) throw new Error('Google API key not found');
      return createGoogleGenerativeAI({
        apiKey: process.env.GEMINI_API_KEY,
        ...(proxyFetch && { fetch: proxyFetch })
      })(model) as LanguageModelV1;
    }

    if (model.startsWith('deepseek')) {
      if (!process.env.DEEPSEEK_API_KEY) throw new Error('DeepSeek API key not found');
      return createDeepSeek({
        apiKey: process.env.DEEPSEEK_API_KEY,
        ...(proxyFetch && { fetch: proxyFetch })
      })(model) as LanguageModelV1;
    }

    if (model.startsWith('gemma')) {
      if (!process.env.GROQ_API_KEY) throw new Error('Groq API key not found');
      return createGroq({
        apiKey: process.env.GROQ_API_KEY,
        ...(proxyFetch && { fetch: proxyFetch })
      })(model) as LanguageModelV1;
    }

    void useThinking;
    // if (model.startsWith('deepseek')) {
    //   if (!process.env.DEEPSEEK_API_KEY) throw new Error('DeepSeek API key not found');
    //   return createDeepSeek({ apiKey: process.env.DEEPSEEK_API_KEY })(model);
    // }

    // Default to OpenAI for Pro
    console.log('🔍 DEBUG: Using default OpenAI for Pro user');
    console.log('- OPENAI_API_KEY present:', !!process.env.OPENAI_API_KEY);
    console.log('- Hardcoded model: gpt-4.1-nano (ignoring selected model)');

    if (!process.env.OPENAI_API_KEY) throw new Error('OpenAI API key not found');
    return createOpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      compatibility: 'strict',
      ...(proxyFetch && { fetch: proxyFetch })
    })('gpt-4.1-nano');
  }

  // Free users can only use gpt-4.1-nano
  if (!config) {
    console.log('🔍 DEBUG: No config provided - using default gpt-4.1-nano');
    console.log('- OPENAI_API_KEY present:', !!process.env.OPENAI_API_KEY);

    if (!process.env.OPENAI_API_KEY) throw new Error('OpenAI API key not found');
    return createOpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      compatibility: 'strict',
      ...(proxyFetch && { fetch: proxyFetch })
    })('gpt-4.1-nano') as LanguageModelV1;
  }

  const { model } = config;

  // Free users can only use gpt-4.1-nano
  if (model === 'gpt-4.1-nano') {
    if (!process.env.OPENAI_API_KEY) throw new Error('OpenAI API key not found');
    return createOpenAI({
      apiKey: process.env.OPENAI_API_KEY,
      compatibility: 'strict',
      ...(proxyFetch && { fetch: proxyFetch })
    })(model) as LanguageModelV1;
  }

  // All other models require Pro subscription
  throw new Error('Please upgrade to Pro to use advanced AI models');
}
